["tests/test_annotated_annotation.py::test_annotated_annotation", "tests/test_app_commands_autocomplete.py::test_bound_function_autocomplete", "tests/test_app_commands_autocomplete.py::test_free_function_autocomplete", "tests/test_app_commands_autocomplete.py::test_group_function_autocomplete", "tests/test_app_commands_autocomplete.py::test_invalid_bound_function_autocomplete", "tests/test_app_commands_autocomplete.py::test_invalid_free_function_autocomplete", "tests/test_app_commands_autocomplete.py::test_multiple_transformer_autocomplete", "tests/test_app_commands_autocomplete.py::test_transformer_autocomplete", "tests/test_app_commands_description.py::test_descriptions_cog_commands", "tests/test_app_commands_description.py::test_descriptions_describe", "tests/test_app_commands_description.py::test_descriptions_docstring_and_describe", "tests/test_app_commands_description.py::test_descriptions_google", "tests/test_app_commands_description.py::test_descriptions_google_extras", "tests/test_app_commands_description.py::test_descriptions_group_args", "tests/test_app_commands_description.py::test_descriptions_group_no_args", "tests/test_app_commands_description.py::test_descriptions_no_args", "tests/test_app_commands_description.py::test_descriptions_numpy", "tests/test_app_commands_description.py::test_descriptions_numpy_extras", "tests/test_app_commands_description.py::test_descriptions_sphinx", "tests/test_app_commands_description.py::test_descriptions_sphinx_extras", "tests/test_app_commands_group.py::test_cog_group_with_commands", "tests/test_app_commands_group.py::test_cog_group_with_custom_state_issue9383", "tests/test_app_commands_group.py::test_cog_group_with_group", "tests/test_app_commands_group.py::test_cog_group_with_subclass_group", "tests/test_app_commands_group.py::test_cog_group_with_subclassed_subclass_group", "tests/test_app_commands_group.py::test_cog_hybrid_group_manual_command", "tests/test_app_commands_group.py::test_cog_hybrid_group_manual_nested_command", "tests/test_app_commands_group.py::test_cog_hybrid_group_wrapped_instance", "tests/test_app_commands_group.py::test_cog_with_commands", "tests/test_app_commands_group.py::test_cog_with_group_subclass_with_commands", "tests/test_app_commands_group.py::test_cog_with_group_subclass_with_group", "tests/test_app_commands_group.py::test_cog_with_group_subclass_with_group_subclass", "tests/test_app_commands_group.py::test_cog_with_group_with_commands", "tests/test_app_commands_group.py::test_cog_with_nested_group_with_commands", "tests/test_app_commands_group.py::test_group_subclass_with_commands", "tests/test_app_commands_group.py::test_group_subclass_with_group", "tests/test_app_commands_group.py::test_group_subclass_with_group_subclass", "tests/test_app_commands_group.py::test_group_with_commands", "tests/test_app_commands_invoke.py::test_invalid_command_invoke[command0]", "tests/test_app_commands_invoke.py::test_invalid_command_invoke[command1]", "tests/test_app_commands_invoke.py::test_invalid_command_invoke[command2]", "tests/test_app_commands_invoke.py::test_invalid_command_invoke[command3]", "tests/test_app_commands_invoke.py::test_invalid_command_invoke[command4]", "tests/test_app_commands_invoke.py::test_invalid_command_invoke[command5]", "tests/test_app_commands_invoke.py::test_invalid_command_invoke[command6]", "tests/test_app_commands_invoke.py::test_invalid_command_invoke[command7]", "tests/test_app_commands_invoke.py::test_valid_command_invoke[command0-None]", "tests/test_app_commands_invoke.py::test_valid_command_invoke[command1-None]", "tests/test_app_commands_invoke.py::test_valid_command_invoke[command2-TypeError]", "tests/test_app_commands_invoke.py::test_valid_command_invoke[command3-TypeError]", "tests/test_app_commands_invoke.py::test_valid_command_invoke[command4-None]", "tests/test_app_commands_invoke.py::test_valid_command_invoke[command5-None]", "tests/test_app_commands_invoke.py::test_valid_command_invoke[command6-TypeError]", "tests/test_app_commands_invoke.py::test_valid_command_invoke[command7-TypeError]", "tests/test_embed.py::test_embed_add_field[music-music value-True]", "tests/test_embed.py::test_embed_add_field[sport-sport value-False]", "tests/test_embed.py::test_embed_clear_fields", "tests/test_embed.py::test_embed_colour_setter_failure[#FFFFFF]", "tests/test_embed.py::test_embed_colour_setter_failure[-0.5]", "tests/test_embed.py::test_embed_copy[title 1-description 1-https://example.com]", "tests/test_embed.py::test_embed_copy[title 2-description 2-None]", "tests/test_embed.py::test_embed_from_dict", "tests/test_embed.py::test_embed_initialization[title-description-11259375-https://example.com]", "tests/test_embed.py::test_embed_initialization[title-description-16716436-None]", "tests/test_embed.py::test_embed_initialization[title-description-colour2-https://example.com]", "tests/test_embed.py::test_embed_initialization[title-description-colour3-None]", "tests/test_embed.py::test_embed_insert_field", "tests/test_embed.py::test_embed_len[title 1-description 1]", "tests/test_embed.py::test_embed_len[title 2-description 2]", "tests/test_embed.py::test_embed_len_with_options[title 1-description 1-fields0-footer 1-author 1]", "tests/test_embed.py::test_embed_len_with_options[title 2-description 2-fields1-footer 2-author 2]", "tests/test_embed.py::test_embed_remove_author", "tests/test_embed.py::test_embed_remove_field", "tests/test_embed.py::test_embed_remove_footer", "tests/test_embed.py::test_embed_set_author[<PERSON><PERSON><PERSON>-http://example.com-None]", "tests/test_embed.py::test_embed_set_author[NCPlayz-None-http://example.com/icon.png]", "tests/test_embed.py::test_embed_set_author[Rapptz-http://example.com-http://example.com/icon.png]", "tests/test_embed.py::test_embed_set_field_at", "tests/test_embed.py::test_embed_set_field_at_failure", "tests/test_embed.py::test_embed_set_footer[Hello discord.py-https://example.com]", "tests/test_embed.py::test_embed_set_footer[None-None]", "tests/test_embed.py::test_embed_set_footer[None-https://example.com]", "tests/test_embed.py::test_embed_set_footer[text-None]", "tests/test_embed.py::test_embed_set_image[None]", "tests/test_embed.py::test_embed_set_image[http://example.com]", "tests/test_embed.py::test_embed_set_thumbnail[None]", "tests/test_embed.py::test_embed_set_thumbnail[http://example.com]", "tests/test_embed.py::test_embed_to_dict", "tests/test_embed.py::test_embed_truthiness[None-False]", "tests/test_embed.py::test_embed_truthiness[test-True]", "tests/test_ext_commands_cog.py::TestCog::test_cog_app_command_error_from_command", "tests/test_ext_commands_cog.py::TestCog::test_cog_app_command_error_from_command_with_error_handler", "tests/test_ext_commands_cog.py::TestCog::test_cog_app_command_error_from_group", "tests/test_ext_commands_cog.py::TestCog::test_cog_app_command_error_from_group_with_handler", "tests/test_ext_commands_cog.py::TestCog::test_cog_app_command_error_from_sub_group", "tests/test_ext_commands_cog.py::TestCog::test_cog_app_command_error_from_sub_group_with_handler_and_parent_handler", "tests/test_ext_commands_cog.py::TestCog::test_cog_app_command_error_from_sub_group_with_parent_handler", "tests/test_ext_commands_cog.py::TestGroupCog::test_cog_app_command_error_from_command", "tests/test_ext_commands_cog.py::TestGroupCog::test_cog_app_command_error_from_command_with_error_handler", "tests/test_ext_commands_cog.py::TestGroupCog::test_cog_app_command_error_from_sub_group", "tests/test_ext_commands_cog.py::TestGroupCog::test_cog_app_command_error_from_sub_group_with_handler", "tests/test_ext_commands_description.py::test_ext_commands_descriptions_cog_commands", "tests/test_ext_commands_description.py::test_ext_commands_descriptions_explicit", "tests/test_ext_commands_description.py::test_ext_commands_descriptions_no_args", "tests/test_ext_commands_description.py::test_ext_commands_descriptions_numpy", "tests/test_ext_commands_description.py::test_ext_commands_descriptions_numpy_extras", "tests/test_ext_tasks.py::test_explicit_initial_runs_tomorrow_multi", "tests/test_ext_tasks.py::test_explicit_initial_runs_tomorrow_single", "tests/test_ext_tasks.py::test_task_date_resolve[dt0-America/New_York-expected0]", "tests/test_ext_tasks.py::test_task_date_resolve[dt1-America/New_York-expected1]", "tests/test_ext_tasks.py::test_task_date_resolve[dt2-America/New_York-expected2]", "tests/test_ext_tasks.py::test_task_date_resolve[dt3-UTC-expected3]", "tests/test_ext_tasks.py::test_task_is_ambiguous", "tests/test_ext_tasks.py::test_task_is_imaginary", "tests/test_ext_tasks.py::test_task_regression_issue7659", "tests/test_ext_tasks.py::test_task_regression_issue7676", "tests/test_files.py::test_file_not_spoiler_with_overriding_name_double_spoiler", "tests/test_files.py::test_file_not_spoiler_with_overriding_name_not_spoiler", "tests/test_files.py::test_file_not_spoiler_with_overriding_name_spoiler", "tests/test_files.py::test_file_reset", "tests/test_files.py::test_file_to_dict", "tests/test_files.py::test_file_with_name", "tests/test_files.py::test_file_with_name_and_double_spoiler_and_implicit_spoiler", "tests/test_files.py::test_file_with_name_and_double_spoiler_and_not_spoiler", "tests/test_files.py::test_file_with_name_and_double_spoiler_and_spoiler", "tests/test_files.py::test_file_with_no_name", "tests/test_files.py::test_file_with_no_name_and_spoiler", "tests/test_files.py::test_file_with_spoiler_name_and_implicit_spoiler", "tests/test_files.py::test_file_with_spoiler_name_and_not_spoiler", "tests/test_files.py::test_file_with_spoiler_name_and_spoiler", "tests/test_files.py::test_file_with_spoiler_with_overriding_name_not_spoiler", "tests/test_files.py::test_file_with_spoiler_with_overriding_name_spoiler", "tests/test_files.py::test_io_failure", "tests/test_files.py::test_io_reset", "tests/test_files.py::test_io_to_dict", "tests/test_files.py::test_io_with_name", "tests/test_files.py::test_io_with_no_name", "tests/test_permissions_all.py::test_permissions_all", "tests/test_ui_buttons.py::test_button_init", "tests/test_ui_buttons.py::test_button_setter", "tests/test_ui_buttons.py::test_button_with_invalid_emoji", "tests/test_ui_buttons.py::test_button_with_partial_emoji", "tests/test_ui_buttons.py::test_button_with_sku_id", "tests/test_ui_buttons.py::test_button_with_str_emoji", "tests/test_ui_buttons.py::test_button_with_url", "tests/test_ui_buttons.py::test_invalid_custom_id", "tests/test_ui_buttons.py::test_invalid_url", "tests/test_ui_buttons.py::test_mix_both_custom_id_and_sku_id", "tests/test_ui_buttons.py::test_mix_both_custom_id_and_url", "tests/test_ui_buttons.py::test_mix_both_url_and_sku_id", "tests/test_ui_modals.py::test_add_item", "tests/test_ui_modals.py::test_add_item_invalid", "tests/test_ui_modals.py::test_maximum_items", "tests/test_ui_modals.py::test_modal_init", "tests/test_ui_modals.py::test_modal_setters", "tests/test_ui_modals.py::test_no_title", "tests/test_ui_modals.py::test_to_dict", "tests/test_ui_selects.py::test_add_option", "tests/test_utils.py::test_as_chunks[source0-2-chunked0]", "tests/test_utils.py::test_as_chunks[source1-3-chunked1]", "tests/test_utils.py::test_as_chunks[source2-4-chunked2]", "tests/test_utils.py::test_as_chunks[source3-5-chunked3]", "tests/test_utils.py::test_cached_properties", "tests/test_utils.py::test_escape_mentions[<@!80088516616269824>]", "tests/test_utils.py::test_escape_mentions[<@&381978264698224660>]", "tests/test_utils.py::test_escape_mentions[<@80088516616269824>]", "tests/test_utils.py::test_escape_mentions[@everyone]", "tests/test_utils.py::test_escape_mentions[@here]", "tests/test_utils.py::test_format_dt[dt0-None-<t:0>]", "tests/test_utils.py::test_format_dt[dt1-None-<t:1577836800>]", "tests/test_utils.py::test_format_dt[dt2-F-<t:1577836800:F>]", "tests/test_utils.py::test_format_dt[dt3-D-<t:2000000000:D>]", "tests/test_utils.py::test_get_find", "tests/test_utils.py::test_get_slots", "tests/test_utils.py::test_is_inside_class", "tests/test_utils.py::test_resolve_annotation[datetime-datetime]", "tests/test_utils.py::test_resolve_annotation[datetime.datetime-datetime]", "tests/test_utils.py::test_resolve_annotation[typing.Union[typing.Literal[\"a\"], typing.Literal[\"b\"]]-Union]", "tests/test_utils.py::test_resolve_annotation[typing.Union[typing.Union[int, str], typing.Union[bool, dict]]-Union]", "tests/test_utils.py::test_resolve_annotation_310[int | None-Optional]", "tests/test_utils.py::test_resolve_annotation_310[str | int | None-Union]", "tests/test_utils.py::test_resolve_annotation_310[str | int-Union]", "tests/test_utils.py::test_resolve_annotation_optional_normalisation", "tests/test_utils.py::test_resolve_annotation_with_cache[datetime-datetime-False]", "tests/test_utils.py::test_resolve_annotation_with_cache[datetime.datetime-datetime-True]", "tests/test_utils.py::test_resolve_annotation_with_cache[typing.Union[typing.Literal[\"a\"], typing.Literal[\"b\"]]-Union-True]", "tests/test_utils.py::test_resolve_annotation_with_cache[typing.Union[typing.Union[int, str], typing.Union[bool, dict]]-Union-True]", "tests/test_utils.py::test_resolve_annotation_with_cache_310[int | None-Optional]", "tests/test_utils.py::test_resolve_annotation_with_cache_310[str | int | None-Union]", "tests/test_utils.py::test_resolve_annotation_with_cache_310[str | int-Union]", "tests/test_utils.py::test_resolve_invite[https://discord.com/invite/dpy-dpy]", "tests/test_utils.py::test_resolve_invite[https://discord.gg/dpy-dpy]", "tests/test_utils.py::test_resolve_invite[https://discordapp.com/invite/dpy-dpy]", "tests/test_utils.py::test_resolve_invite_event[https://discord.com/invite/dpy-None]", "tests/test_utils.py::test_resolve_invite_event[https://discord.com/invite/dpy?event=4098-4098]", "tests/test_utils.py::test_resolve_invite_event[https://discord.gg/dpy-None]", "tests/test_utils.py::test_resolve_invite_event[https://discord.gg/dpy?event=727-727]", "tests/test_utils.py::test_resolve_invite_event[https://discordapp.com/invite/dpy-None]", "tests/test_utils.py::test_resolve_invite_event[https://discordapp.com/invite/dpy?event=22222222-22222222]", "tests/test_utils.py::test_resolve_template[https://discord.com/template/foobar-foobar]", "tests/test_utils.py::test_resolve_template[https://discord.new/foobar-foobar]", "tests/test_utils.py::test_resolve_template[https://discordapp.com/template/foobar-foobar]", "tests/test_utils.py::test_snowflake_time[10000000000000000-time_tuple0]", "tests/test_utils.py::test_snowflake_time[100000000000000000-time_tuple2]", "tests/test_utils.py::test_snowflake_time[1000000000000000000-time_tuple5]", "tests/test_utils.py::test_snowflake_time[12345678901234567-time_tuple1]", "tests/test_utils.py::test_snowflake_time[123456789012345678-time_tuple3]", "tests/test_utils.py::test_snowflake_time[661720302316814366-time_tuple4]", "tests/test_utils.py::test_valid_icon_size"]